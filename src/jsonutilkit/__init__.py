"""
JSONUtilKit - A lightweight Python toolkit for JSON manipulation.

This package provides utilities for reading, writing, validating, and manipulating JSON data.
"""

__version__ = "0.1.0"
__author__ = "Armaan Shahpuri"
__email__ = "<EMAIL>"

from .core import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .parser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .validator import JSONValidator
from .ai_processor import AIJSONProcessor
from .extractor import JSONExtractor
from .cleaner import <PERSON><PERSON><PERSON>lean<PERSON>
from .transformer import JSONTransformer
from .schema_generator import SchemaGenerator

__all__ = [
    "JSO<PERSON>Hand<PERSON>",
    "JSONValidator",
    "JSONParser",
    "AIJSONProcessor",
    "JSONExtractor",
    "<PERSON><PERSON><PERSON>lean<PERSON>",
    "JSONTransformer",
    "SchemaGenerator",
]
