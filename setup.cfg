[metadata]
name = jsonutilkit
version = attr: jsonutilkit.__version__
description = A lightweight Python toolkit for modifying, reading, and validating JSON
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/yourusername/jsonutilkit
author = Your Name
author_email = <EMAIL>
license = MIT
license_files = LICENSE
classifiers =
    Development Status :: 3 - Alpha
    Intended Audience :: Developers
    License :: OSI Approved :: MIT License
    Operating System :: OS Independent
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Programming Language :: Python :: 3.12
    Topic :: Software Development :: Libraries :: Python Modules
    Topic :: Utilities

[options]
packages = find:
package_dir =
    = src
python_requires = >=3.8
include_package_data = True
zip_safe = False

[options.packages.find]
where = src

[options.extras_require]
dev =
    pytest>=7.0
    pytest-cov>=4.0
    black>=23.0
    isort>=5.0
    flake8>=6.0
    mypy>=1.0
    pre-commit>=3.0
docs =
    sphinx>=5.0
    sphinx-rtd-theme>=1.0

[flake8]
max-line-length = 88
extend-ignore = E203, W503
exclude =
    .git,
    __pycache__,
    build,
    dist,
    *.egg-info,
    .venv,
    .tox

[bdist_wheel]
universal = 0
